# n8n SSL Certificate Issue Resolution Guide

## Problem Description
You're encountering SSL certificate errors when making HTTPS requests from n8n workflows due to corporate network certificate policies.

## Solutions (Ordered by Security Level)

### Solution 1: Use n8n's Built-in "Ignore SSL Issues" Option (Per-Node)

**Steps:**
1. Open your HTTP Request node in n8n
2. Scroll down to the "Options" section
3. Find the "Ignore SSL Issues" toggle
4. Enable this option
5. Save and execute your workflow

**Pros:** 
- Secure for other nodes
- Easy to implement
- Node-specific control

**Cons:** 
- Must be set for each HTTP Request node
- Easy to forget when creating new nodes

### Solution 2: Start n8n with SSL Disabled (Recommended for Corporate Networks)

**Option A: Using Batch File**
1. Use the provided `start-n8n-ssl-disabled.bat` file
2. Double-click to run n8n with SSL disabled
3. Access n8n at http://localhost:5678

**Option B: Using PowerShell**
1. Run the provided `start-n8n-ssl-disabled.ps1` script
2. Or manually run:
   ```powershell
   $env:NODE_TLS_REJECT_UNAUTHORIZED = "0"
   npx n8n
   ```

**Option C: Using Command Line**
```cmd
set NODE_TLS_REJECT_UNAUTHORIZED=0
npx n8n
```

**Pros:**
- Works globally for all HTTP requests
- No need to configure individual nodes
- Simple to implement

**Cons:**
- Disables SSL verification for all requests
- Less secure (but acceptable in corporate environments)

### Solution 3: Environment Variable (Permanent)

**Windows (System-wide):**
1. Open System Properties → Advanced → Environment Variables
2. Add new system variable:
   - Name: `NODE_TLS_REJECT_UNAUTHORIZED`
   - Value: `0`
3. Restart your terminal/command prompt
4. Run `npx n8n` normally

**PowerShell (Session-specific):**
```powershell
[Environment]::SetEnvironmentVariable("NODE_TLS_REJECT_UNAUTHORIZED", "0", "User")
```

### Solution 4: Corporate Certificate Configuration (Most Secure)

If your IT department provides corporate certificates:

1. Export corporate root certificates
2. Set NODE_EXTRA_CA_CERTS environment variable:
   ```cmd
   set NODE_EXTRA_CA_CERTS=C:\path\to\corporate-certs.pem
   ```
3. Run n8n normally

## Testing Your Solution

1. Start n8n using your chosen method
2. Create a simple workflow with an HTTP Request node
3. Set URL to: `https://jsonplaceholder.typicode.com/users/1`
4. Execute the workflow
5. Verify you receive JSON data without SSL errors

## Security Considerations

- **Development/Corporate Networks**: Using `NODE_TLS_REJECT_UNAUTHORIZED=0` is acceptable
- **Production**: Always use proper SSL certificates and validation
- **Hybrid Approach**: Use per-node "Ignore SSL Issues" for specific trusted endpoints

## Troubleshooting

If you still encounter issues:
1. Verify n8n is running on the correct port (5678)
2. Check Windows Firewall settings
3. Verify corporate proxy settings
4. Contact your IT department for corporate certificate information

## Files Created
- `start-n8n-ssl-disabled.bat` - Batch file to start n8n with SSL disabled
- `start-n8n-ssl-disabled.ps1` - PowerShell script to start n8n with SSL disabled
- `n8n-config.json` - Updated n8n configuration file
