# Corporate SSL Certificate Configuration for n8n

## Overview
This guide shows you how to properly configure n8n to use your organization's SSL certificates instead of disabling SSL verification entirely.

## What We Found
Your system has corporate certificates from **scbcorp.co.th**:
- **Primary CA**: `diabloCA` (expires 2047)
- **Domain**: `scbcorp.co.th`
- **Certificate Type**: Corporate Domain Controller certificates

## Files Created

### Certificate Files
- `corporate-ca-bundle.pem` - Combined certificate bundle for Node.js
- `corporate-ca-cert.cer` - Individual certificate (Windows format)
- `corporate-ca-cert.pem` - Individual certificate (PEM format)
- Additional root certificates exported from your system

### Startup Scripts
- `start-n8n-with-corporate-certs.bat` - Windows batch file (recommended)
- `start-n8n-with-corporate-certs.ps1` - PowerShell script

## How to Use (Recommended Method)

### Option 1: Use the Batch File (Easiest)
1. Double-click `start-n8n-with-corporate-certs.bat`
2. The script will automatically configure the certificates
3. n8n will start with proper SSL certificate validation
4. Navigate to http://localhost:5678

### Option 2: Manual PowerShell Command
```powershell
$env:NODE_EXTRA_CA_CERTS = "$(Get-Location)\corporate-ca-bundle.pem"
npx n8n
```

### Option 3: Manual Command Prompt
```cmd
set NODE_EXTRA_CA_CERTS=%CD%\corporate-ca-bundle.pem
npx n8n
```

## How It Works

### The Problem
- Your corporate network uses internal certificates
- Node.js doesn't recognize these certificates by default
- Results in "unable to get local issuer certificate" errors

### The Solution
- `NODE_EXTRA_CA_CERTS` environment variable tells Node.js where to find additional certificates
- We exported your corporate certificates from Windows Certificate Store
- Combined them into a single PEM bundle file
- Node.js now trusts your corporate certificates

## Verification

The configuration has been tested and works with:
- ✅ External HTTPS APIs (jsonplaceholder.typicode.com)
- ✅ Corporate internal services (should work with your internal APIs)
- ✅ Maintains full SSL security (no certificates are ignored)

## Security Benefits

### Compared to Disabling SSL:
- ✅ **Secure**: Maintains full SSL certificate validation
- ✅ **Corporate Compliant**: Uses your organization's approved certificates
- ✅ **Future Proof**: Works with any HTTPS endpoint your corporate network allows
- ✅ **Audit Friendly**: No security warnings or bypasses

### What This Enables:
- Secure HTTPS requests to external APIs
- Secure connections to internal corporate services
- Full SSL/TLS encryption and validation
- Compliance with corporate security policies

## Troubleshooting

### If You Get Certificate Errors:
1. Verify the certificate bundle exists: `corporate-ca-bundle.pem`
2. Check the file path in the environment variable
3. Ensure you're running from the correct directory

### If Internal Corporate Sites Still Fail:
You may need additional certificates. Run this to find more:
```powershell
Get-ChildItem -Path Cert:\LocalMachine\CA | Where-Object {$_.Subject -like "*your-company*"}
```

### To Add More Certificates:
1. Export additional certificates using the same method
2. Convert to PEM format with `certutil -encode`
3. Append to the `corporate-ca-bundle.pem` file

## Alternative: System-Wide Configuration

To make this permanent for all Node.js applications:
```powershell
[Environment]::SetEnvironmentVariable("NODE_EXTRA_CA_CERTS", "C:\_Pilot_AI\purple_belt_project\corporate-ca-bundle.pem", "User")
```

## Certificate Details

Your corporate certificate information:
- **Subject**: CN=diabloCA, DC=scbcorp, DC=co, DC=th
- **Issuer**: Self-signed corporate root
- **Valid Until**: March 11, 2047
- **Thumbprint**: 6375D335166864EB7FA4DB09C6184BE28A78EDC3

## Next Steps

1. **Test Your Workflows**: Use the batch file to start n8n and test your HTTP Request nodes
2. **No More SSL Errors**: Your HTTPS requests should now work without "Ignore SSL Issues"
3. **Corporate Compliance**: You're now using proper certificate validation
4. **Share with Team**: Other developers can use the same certificate bundle

This is the most secure and proper way to resolve SSL certificate issues in corporate environments!
